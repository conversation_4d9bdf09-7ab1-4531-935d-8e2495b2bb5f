# Test Environment Configuration
NODE_ENV=test
PORT=3002

# M-Pesa Test Configuration (Sandbox)
MPESA_ENVIRONMENT=sandbox
MPESA_CONSUMER_KEY=test_consumer_key
MPESA_CONSUMER_SECRET=test_consumer_secret
MPESA_BUSINESS_SHORT_CODE=174379
MPESA_PASSKEY=test_passkey
MPESA_INITIATOR_NAME=testapi
MPESA_SECURITY_CREDENTIAL=test_security_credential

# M-Pesa Test URLs
MPESA_BASE_URL=https://sandbox.safaricom.co.ke
MPESA_AUTH_URL=https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials
MPESA_STK_PUSH_URL=https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest
MPESA_STK_QUERY_URL=https://sandbox.safaricom.co.ke/mpesa/stkpushquery/v1/query

# Test Database
DATABASE_URL=:memory:
DATABASE_TYPE=sqlite

# Test Blockchain Configuration
BLOCKCHAIN_RPC_URL=http://localhost:8545
BLOCKCHAIN_CHAIN_ID=31337
USDT_CONTRACT_ADDRESS=******************************************
PACKAGE_MANAGER_CONTRACT_ADDRESS=******************************************
TREASURY_WALLET_ADDRESS=******************************************
TREASURY_PRIVATE_KEY=******************************************123456789012345678901234

# Test Security
JWT_SECRET=test_jwt_secret
API_KEY=test_api_key
API_RATE_LIMIT=1000
CORS_ORIGIN=http://localhost:3000

# Test Callback URLs
CALLBACK_BASE_URL=http://localhost:3002
MPESA_CALLBACK_URL=http://localhost:3002/api/mpesa/callback
MPESA_TIMEOUT_URL=http://localhost:3002/api/mpesa/timeout

# Test Logging
LOG_LEVEL=error
LOG_FILE=./tests/logs/test.log

# Test Exchange Rates
KES_TO_USD_RATE=0.0067
USD_TO_KES_RATE=149.25
