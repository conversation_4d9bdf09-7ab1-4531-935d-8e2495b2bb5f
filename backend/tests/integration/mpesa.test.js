import { jest } from '@jest/globals';
import request from 'supertest';
import app from '../../src/server.js';
import { Transaction } from '../../src/models/Transaction.js';
import mpesaService from '../../src/services/mpesaService.js';

// Mock the M-Pesa service
jest.mock('../../src/services/mpesaService.js');

describe('M-Pesa API Integration Tests', () => {
  const testWalletAddress = '******************************************';
  const testPhoneNumber = '254712345678';
  const testPackageId = 1;
  const testAmount = 100;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/mpesa/initiate-payment', () => {
    const validPaymentData = {
      walletAddress: testWalletAddress,
      packageId: testPackageId,
      phoneNumber: testPhoneNumber,
      amount: testAmount
    };

    it('should successfully initiate M-Pesa payment', async () => {
      // Mock successful M-Pesa response
      mpesaService.initiateSTKPush.mockResolvedValue({
        success: true,
        checkoutRequestId: 'ws_CO_123456789',
        merchantRequestId: 'merchant_123456789',
        responseDescription: 'Success. Request accepted for processing'
      });

      const response = await request(app)
        .post('/api/mpesa/initiate-payment')
        .send(validPaymentData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.transactionId).toBeTruthy();
      expect(response.body.checkoutRequestId).toBe('ws_CO_123456789');
      expect(response.body.message).toContain('Payment request sent');
      expect(response.body.amount.usd).toBe(testAmount);
      expect(response.body.amount.kes).toBe(testAmount * 149.25);
    });

    it('should handle M-Pesa API failure', async () => {
      mpesaService.initiateSTKPush.mockResolvedValue({
        success: false,
        error: 'Insufficient funds'
      });

      const response = await request(app)
        .post('/api/mpesa/initiate-payment')
        .send(validPaymentData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Insufficient funds');
      expect(response.body.errorType).toBeTruthy();
    });

    it('should validate wallet address', async () => {
      const invalidData = {
        ...validPaymentData,
        walletAddress: 'invalid-address'
      };

      const response = await request(app)
        .post('/api/mpesa/initiate-payment')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toContain('Invalid wallet address');
    });

    it('should validate phone number format', async () => {
      const invalidData = {
        ...validPaymentData,
        phoneNumber: '0712345678' // Should be 254712345678
      };

      const response = await request(app)
        .post('/api/mpesa/initiate-payment')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toContain('Validation failed');
    });

    it('should validate amount', async () => {
      const invalidData = {
        ...validPaymentData,
        amount: -10
      };

      const response = await request(app)
        .post('/api/mpesa/initiate-payment')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toContain('Validation failed');
    });

    it('should validate package ID', async () => {
      const invalidData = {
        ...validPaymentData,
        packageId: 0
      };

      const response = await request(app)
        .post('/api/mpesa/initiate-payment')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toContain('Validation failed');
    });

    it('should handle minimum amount validation', async () => {
      const invalidData = {
        ...validPaymentData,
        amount: 0.001 // Too small, results in KES < 1
      };

      const response = await request(app)
        .post('/api/mpesa/initiate-payment')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toContain('Amount too small');
    });

    it('should apply rate limiting', async () => {
      mpesaService.initiateSTKPush.mockResolvedValue({
        success: true,
        checkoutRequestId: 'ws_CO_123456789',
        merchantRequestId: 'merchant_123456789'
      });

      // Make multiple requests quickly
      const requests = Array(5).fill().map(() =>
        request(app)
          .post('/api/mpesa/initiate-payment')
          .send(validPaymentData)
      );

      const responses = await Promise.all(requests);
      
      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('POST /api/mpesa/callback/:transactionId', () => {
    let testTransaction;

    beforeEach(() => {
      // Create a test transaction
      testTransaction = global.testUtils.createTestTransaction({
        checkoutRequestId: 'ws_CO_123456789'
      });
    });

    it('should handle successful payment callback', async () => {
      const callbackData = {
        Body: {
          stkCallback: {
            MerchantRequestID: 'merchant_123456789',
            CheckoutRequestID: 'ws_CO_123456789',
            ResultCode: 0,
            ResultDesc: 'The service request is processed successfully.',
            CallbackMetadata: {
              Item: [
                { Name: 'Amount', Value: 1000 },
                { Name: 'MpesaReceiptNumber', Value: 'NLJ7RT61SV' },
                { Name: 'TransactionDate', Value: 20231201120000 },
                { Name: 'PhoneNumber', Value: 254712345678 }
              ]
            }
          }
        }
      };

      // Mock parseCallbackData
      mpesaService.parseCallbackData.mockReturnValue({
        merchantRequestId: 'merchant_123456789',
        checkoutRequestId: 'ws_CO_123456789',
        resultCode: 0,
        resultDesc: 'The service request is processed successfully.',
        amount: 1000,
        mpesaReceiptNumber: 'NLJ7RT61SV',
        transactionDate: 20231201120000,
        phoneNumber: 254712345678
      });

      const response = await request(app)
        .post(`/api/mpesa/callback/${testTransaction.id}`)
        .send(callbackData)
        .expect(200);

      expect(response.body.ResultCode).toBe(0);
      expect(response.body.ResultDesc).toBe('Success');
    });

    it('should handle failed payment callback', async () => {
      const callbackData = {
        Body: {
          stkCallback: {
            MerchantRequestID: 'merchant_123456789',
            CheckoutRequestID: 'ws_CO_123456789',
            ResultCode: 1032,
            ResultDesc: 'Request cancelled by user'
          }
        }
      };

      mpesaService.parseCallbackData.mockReturnValue({
        merchantRequestId: 'merchant_123456789',
        checkoutRequestId: 'ws_CO_123456789',
        resultCode: 1032,
        resultDesc: 'Request cancelled by user'
      });

      const response = await request(app)
        .post(`/api/mpesa/callback/${testTransaction.id}`)
        .send(callbackData)
        .expect(200);

      expect(response.body.ResultCode).toBe(0);
      expect(response.body.ResultDesc).toBe('Success');
    });

    it('should handle callback for non-existent transaction', async () => {
      const callbackData = {
        Body: {
          stkCallback: {
            MerchantRequestID: 'merchant_123456789',
            CheckoutRequestID: 'ws_CO_123456789',
            ResultCode: 0
          }
        }
      };

      const response = await request(app)
        .post('/api/mpesa/callback/non-existent-id')
        .send(callbackData)
        .expect(200); // Should still return success to M-Pesa

      expect(response.body.ResultCode).toBe(0);
    });
  });

  describe('GET /api/mpesa/status/:checkoutRequestId', () => {
    it('should query payment status successfully', async () => {
      const checkoutRequestId = 'ws_CO_123456789';
      
      // Mock M-Pesa query response
      mpesaService.querySTKPush.mockResolvedValue({
        success: true,
        data: {
          ResponseCode: '0',
          ResultCode: '0',
          ResultDesc: 'The service request is processed successfully.'
        }
      });

      const response = await request(app)
        .get(`/api/mpesa/status/${checkoutRequestId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.transaction).toBeTruthy();
      expect(response.body.mpesaStatus).toBeTruthy();
    });

    it('should handle query failure', async () => {
      const checkoutRequestId = 'ws_CO_123456789';
      
      mpesaService.querySTKPush.mockResolvedValue({
        success: false,
        error: 'Query failed'
      });

      const response = await request(app)
        .get(`/api/mpesa/status/${checkoutRequestId}`)
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Query failed');
    });

    it('should handle non-existent transaction', async () => {
      const checkoutRequestId = 'non-existent';

      const response = await request(app)
        .get(`/api/mpesa/status/${checkoutRequestId}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Transaction not found');
    });
  });

  describe('GET /api/mpesa/transactions/:walletAddress', () => {
    it('should get transaction history for wallet', async () => {
      const response = await request(app)
        .get(`/api/mpesa/transactions/${testWalletAddress}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.transactions)).toBe(true);
      expect(response.body.stats).toBeTruthy();
    });

    it('should handle pagination', async () => {
      const response = await request(app)
        .get(`/api/mpesa/transactions/${testWalletAddress}`)
        .query({ limit: 5, offset: 10 })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.transactions)).toBe(true);
    });

    it('should apply rate limiting to transaction history', async () => {
      // Make multiple requests quickly
      const requests = Array(10).fill().map(() =>
        request(app).get(`/api/mpesa/transactions/${testWalletAddress}`)
      );

      const responses = await Promise.all(requests);
      
      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('POST /api/mpesa/timeout/:transactionId', () => {
    it('should handle timeout callback', async () => {
      const testTransaction = global.testUtils.createTestTransaction();

      const response = await request(app)
        .post(`/api/mpesa/timeout/${testTransaction.id}`)
        .send({})
        .expect(200);

      expect(response.body.ResultCode).toBe(0);
      expect(response.body.ResultDesc).toBe('Success');
    });
  });
});
